<?= $this->extend('layouts/user_dashboard') ?>

<?= $this->section('title') ?>Dashboard<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Welcome Back, <?= $user['name'] ?>!<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Dashboard</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <!-- Main Content Area -->
    <div class="col-md-8">
        <!-- Wallet & Stats Card -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-wallet me-2"></i>Account Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="fas fa-wallet text-success fs-4"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 text-success">₹<?= number_format($wallet['balance'] ?? 0, 2) ?></h4>
                                <small class="text-muted">Wallet Balance</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="fas fa-list-alt text-primary fs-4"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 text-primary"><?= count($bookings) ?></h4>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="bg-info bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="fas fa-check-circle text-info fs-4"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 text-info"><?= count(array_filter($bookings, function ($b) {
                                                                return $b['status'] == 'confirmed';
                                                            })) ?></h4>
                                <small class="text-muted">Confirmed Orders</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 text-warning"><?= count(array_filter($bookings, function ($b) {
                                                                    return $b['status'] == 'pending';
                                                                })) ?></h4>
                                <small class="text-muted">Pending Orders</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('/booking/create') ?>" class="btn btn-success btn-lg">
                        <i class="fas fa-calendar-plus me-2"></i>Book New Tiffin
                    </a>
                    <a href="<?= base_url('/dishes') ?>" class="btn btn-info btn-lg">
                        <i class="fas fa-utensils me-2"></i>Browse Menu
                    </a>
                    <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus-circle me-2"></i>Recharge Wallet
                    </a>
                    <a href="<?= base_url('/user/profile') ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-user-edit me-2"></i>Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3 text-white fw-bold" style="width: 80px; height: 80px; font-size: 2rem;">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </div>
                    <h6 class="fw-bold mb-1"><?= $user['name'] ?></h6>
                    <small class="text-muted"><?= $user['email'] ?></small>
                </div>

                <hr>

                <p><strong>Phone:</strong> <?= $user['phone'] ?: 'Not provided' ?></p>
                <p><strong>Address:</strong> <?= $user['address'] ? (strlen($user['address']) > 50 ? substr($user['address'], 0, 50) . '...' : $user['address']) : 'Not provided' ?></p>

                <hr>

                <div class="alert alert-success">
                    <h6><i class="fas fa-info-circle"></i> Account Status</h6>
                    <p class="mb-0">Your account is active and ready to place orders!</p>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb"></i> Quick Tip</h6>
                    <p class="mb-0">Keep your wallet topped up for faster checkout experience.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings Section -->
<div class="row mt-4">
    <div class="col-12">

        <!-- Recent Bookings -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Recent Bookings</h5>
            </div>
            <div class="card-body">
                <?php if (empty($bookings)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No Bookings Yet</h5>
                        <p class="text-muted mb-4">You haven't made any bookings yet. Start by exploring our delicious menu!</p>
                        <a href="<?= base_url('/booking/create') ?>" class="btn btn-success btn-lg">
                            <i class="fas fa-calendar-plus me-2"></i>Book Your First Tiffin
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($bookings, 0, 5) as $booking): ?>
                                    <tr>
                                        <td><strong>#<?= $booking['id'] ?></strong></td>
                                        <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                                        <td><strong>₹<?= number_format($booking['total_amount'], 2) ?></strong></td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusIcon = '';
                                            switch ($booking['status']) {
                                                case 'pending':
                                                    $statusClass = 'warning';
                                                    $statusIcon = 'clock';
                                                    break;
                                                case 'confirmed':
                                                    $statusClass = 'success';
                                                    $statusIcon = 'check-circle';
                                                    break;
                                                case 'delivered':
                                                    $statusClass = 'info';
                                                    $statusIcon = 'check-double';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'danger';
                                                    $statusIcon = 'times-circle';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusIcon = 'question-circle';
                                            }
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?> px-3 py-2">
                                                <i class="fas fa-<?= $statusIcon ?> me-1"></i><?= ucfirst($booking['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="<?= base_url('/user/bookings/view/' . $booking['id']) ?>" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('/user/invoice/' . $booking['id']) ?>" class="btn btn-outline-success" title="View Invoice">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                                <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                                    <a href="<?= base_url('/user/bookings/cancel/' . $booking['id']) ?>" class="btn btn-outline-danger" title="Cancel Booking" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (count($bookings) > 5): ?>
                        <div class="text-center mt-3">
                            <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-list me-2"></i>View All Bookings
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        <?= $this->endSection() ?>