<?= $this->extend('layouts/user_dashboard') ?>

<?= $this->section('title') ?>Dashboard<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Welcome Back, <?= $user['name'] ?>!<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Dashboard</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3>₹<?= number_format($wallet['balance'] ?? 0, 2) ?></h3>
                    <p class="mb-0">Wallet Balance</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-wallet fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?= count($bookings) ?></h3>
                    <p class="mb-0">Total Bookings</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-list-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?= count(array_filter($bookings, function ($b) {
                            return $b['status'] == 'confirmed';
                        })) ?></h3>
                    <p class="mb-0">Confirmed Orders</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3><?= count(array_filter($bookings, function ($b) {
                            return $b['status'] == 'pending';
                        })) ?></h3>
                    <p class="mb-0">Pending Orders</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('/booking/create') ?>" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-calendar-plus me-2"></i>
                            Book New Tiffin
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('/dishes') ?>" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-utensils me-2"></i>
                            Browse Menu
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus-circle me-2"></i>
                            Recharge Wallet
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('/user/profile') ?>" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-user-edit me-2"></i>
                            Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-user"></i> Profile Summary</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; color: white; font-weight: bold;">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </div>
                    <h6><?= $user['name'] ?></h6>
                    <small class="text-muted"><?= $user['email'] ?></small>
                </div>
                <hr>
                <p class="mb-2"><strong>Phone:</strong> <?= $user['phone'] ?: 'Not provided' ?></p>
                <p class="mb-0"><strong>Address:</strong> <?= $user['address'] ? (strlen($user['address']) > 30 ? substr($user['address'], 0, 30) . '...' : $user['address']) : 'Not provided' ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Recent Bookings</h5>
    </div>
    <div class="card-body">
        <?php if (empty($bookings)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No Bookings Yet</h5>
                <p class="text-muted mb-4">You haven't made any bookings yet. Start by exploring our delicious menu!</p>
                <a href="<?= base_url('/booking/create') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-plus me-2"></i>Book Your First Tiffin
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Booking ID</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($bookings, 0, 5) as $booking): ?>
                            <tr>
                                <td><strong>#<?= $booking['id'] ?></strong></td>
                                <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                                <td><strong>₹<?= number_format($booking['total_amount'], 2) ?></strong></td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch ($booking['status']) {
                                        case 'pending':
                                            $statusClass = 'warning';
                                            $statusIcon = 'clock';
                                            break;
                                        case 'confirmed':
                                            $statusClass = 'success';
                                            $statusIcon = 'check-circle';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'info';
                                            $statusIcon = 'check-double';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'danger';
                                            $statusIcon = 'times-circle';
                                            break;
                                        default:
                                            $statusClass = 'secondary';
                                            $statusIcon = 'question-circle';
                                    }
                                    ?>
                                    <span class="badge bg-<?= $statusClass ?> px-3 py-2">
                                        <i class="fas fa-<?= $statusIcon ?> me-1"></i><?= ucfirst($booking['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="<?= base_url('/user/bookings/view/' . $booking['id']) ?>" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('/user/invoice/' . $booking['id']) ?>" class="btn btn-outline-success" title="View Invoice">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                            <a href="<?= base_url('/user/bookings/cancel/' . $booking['id']) ?>" class="btn btn-outline-danger" title="Cancel Booking" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php if (count($bookings) > 5): ?>
                <div class="text-center mt-3">
                    <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-list me-2"></i>View All Bookings
                    </a>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
<?= $this->endSection() ?>