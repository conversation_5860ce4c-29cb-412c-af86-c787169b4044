<?= $this->extend('layouts/user_dashboard') ?>

<?= $this->section('title') ?>Dashboard<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Welcome Back, <?= $user['name'] ?>!<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Dashboard</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body bg-success text-white rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="fw-bold mb-1">₹<?= number_format($wallet['balance'] ?? 0, 2) ?></h3>
                        <p class="mb-0 opacity-75">Wallet Balance</p>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body bg-primary text-white rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="fw-bold mb-1"><?= count($bookings) ?></h3>
                        <p class="mb-0 opacity-75">Total Bookings</p>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-list-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body bg-info text-white rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="fw-bold mb-1"><?= count(array_filter($bookings, function ($b) {
                                                        return $b['status'] == 'confirmed';
                                                    })) ?></h3>
                        <p class="mb-0 opacity-75">Confirmed Orders</p>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body bg-warning text-white rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="fw-bold mb-1"><?= count(array_filter($bookings, function ($b) {
                                                        return $b['status'] == 'pending';
                                                    })) ?></h3>
                        <p class="mb-0 opacity-75">Pending Orders</p>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white border-0">
                <h5 class="mb-0 fw-bold"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="<?= base_url('/booking/create') ?>" class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-calendar-plus me-2"></i>
                            Book New Tiffin
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="<?= base_url('/dishes') ?>" class="btn btn-info btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-utensils me-2"></i>
                            Browse Menu
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-success btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-plus-circle me-2"></i>
                            Recharge Wallet
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="<?= base_url('/user/profile') ?>" class="btn btn-warning btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-user-edit me-2"></i>
                            Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white border-0">
                <h5 class="mb-0 fw-bold"><i class="fas fa-user me-2"></i>Profile Summary</h5>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3 text-white fw-bold" style="width: 80px; height: 80px; font-size: 2rem;">
                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                    </div>
                    <h6 class="fw-bold mb-1"><?= $user['name'] ?></h6>
                    <small class="text-muted"><?= $user['email'] ?></small>
                </div>
                <hr class="my-3">
                <div class="mb-3">
                    <strong class="text-dark">Phone:</strong>
                    <span class="text-muted ms-2"><?= $user['phone'] ?: 'Not provided' ?></span>
                </div>
                <div class="mb-0">
                    <strong class="text-dark">Address:</strong>
                    <span class="text-muted ms-2"><?= $user['address'] ? (strlen($user['address']) > 30 ? substr($user['address'], 0, 30) . '...' : $user['address']) : 'Not provided' ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Recent Bookings</h5>
    </div>
    <div class="card-body">
        <?php if (empty($bookings)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No Bookings Yet</h5>
                <p class="text-muted mb-4">You haven't made any bookings yet. Start by exploring our delicious menu!</p>
                <a href="<?= base_url('/booking/create') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-plus me-2"></i>Book Your First Tiffin
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Booking ID</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($bookings, 0, 5) as $booking): ?>
                            <tr>
                                <td><strong>#<?= $booking['id'] ?></strong></td>
                                <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                                <td><strong>₹<?= number_format($booking['total_amount'], 2) ?></strong></td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch ($booking['status']) {
                                        case 'pending':
                                            $statusClass = 'warning';
                                            $statusIcon = 'clock';
                                            break;
                                        case 'confirmed':
                                            $statusClass = 'success';
                                            $statusIcon = 'check-circle';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'info';
                                            $statusIcon = 'check-double';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'danger';
                                            $statusIcon = 'times-circle';
                                            break;
                                        default:
                                            $statusClass = 'secondary';
                                            $statusIcon = 'question-circle';
                                    }
                                    ?>
                                    <span class="badge bg-<?= $statusClass ?> px-3 py-2">
                                        <i class="fas fa-<?= $statusIcon ?> me-1"></i><?= ucfirst($booking['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="<?= base_url('/user/bookings/view/' . $booking['id']) ?>" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('/user/invoice/' . $booking['id']) ?>" class="btn btn-outline-success" title="View Invoice">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                            <a href="<?= base_url('/user/bookings/cancel/' . $booking['id']) ?>" class="btn btn-outline-danger" title="Cancel Booking" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php if (count($bookings) > 5): ?>
                <div class="text-center mt-3">
                    <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-list me-2"></i>View All Bookings
                    </a>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
<?= $this->endSection() ?>