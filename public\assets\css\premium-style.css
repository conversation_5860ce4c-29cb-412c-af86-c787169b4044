/* Premium Tiffin Delight Styles */

/* Enhanced Color Palette */
:root {
    /* Premium Brand Colors */
    --premium-primary: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
    --premium-secondary: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    --premium-accent: linear-gradient(135deg, #FFC107 0%, #FFD54F 100%);
    --premium-dark: linear-gradient(135deg, #263238 0%, #37474F 100%);
    
    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Premium Shadows */
    --shadow-premium: 0 20px 40px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 30px 60px rgba(0, 0, 0, 0.15);
    --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.08);
    
    /* Premium Gradients */
    --gradient-hero: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(46, 125, 50, 0.9) 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    --gradient-button: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
}

/* Premium Typography */
.premium-title {
    background: var(--premium-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.premium-subtitle {
    color: var(--gray-color);
    font-weight: 500;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* Glass Morphism Cards */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: var(--transition);
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

/* Premium Navigation */
.navbar.premium {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.navbar.premium .navbar-brand {
    background: var(--premium-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.5rem;
}

/* Premium Buttons */
.btn-premium {
    background: var(--gradient-button);
    border: none;
    border-radius: var(--border-radius-pill);
    padding: 12px 30px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition);
}

.btn-premium:hover::before {
    left: 100%;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-premium);
    color: white;
}

/* Premium Hero Section */
.hero-premium {
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Premium Cards */
.premium-card {
    background: var(--gradient-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-card);
    border: none;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--premium-primary);
}

.premium-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
}

.premium-card .card-img-top {
    transition: var(--transition);
}

.premium-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Premium Features */
.feature-premium {
    text-align: center;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    background: white;
    box-shadow: var(--shadow-card);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-premium::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: var(--premium-primary);
    opacity: 0;
    transition: var(--transition);
    transform: rotate(45deg);
}

.feature-premium:hover::before {
    opacity: 0.05;
}

.feature-premium:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-icon-premium {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--premium-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    position: relative;
    z-index: 2;
}

/* Premium Menu Cards */
.menu-card-premium {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-card);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: none;
}

.menu-card-premium:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.menu-card-premium .menu-img-container {
    position: relative;
    overflow: hidden;
}

.menu-card-premium .menu-img-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
}

.menu-card-premium:hover .menu-img-container::after {
    opacity: 1;
}

/* Premium Price Badge */
.price-badge-premium {
    background: var(--premium-primary);
    color: white;
    padding: 8px 16px;
    border-radius: var(--border-radius-pill);
    font-weight: 700;
    font-size: 1.1rem;
    position: absolute;
    bottom: 15px;
    right: 15px;
    box-shadow: var(--shadow-card);
}

/* Premium Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Premium Testimonials */
.testimonial-premium {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    position: relative;
    margin-top: 2rem;
}

.testimonial-premium::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--primary-color);
    font-family: serif;
    opacity: 0.3;
}

/* Premium Footer */
.footer-premium {
    background: var(--premium-dark);
    color: white;
    position: relative;
    overflow: hidden;
}

.footer-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

/* ===== HOME PAGE STYLES ===== */

/* Main Container */
.home-container {
    overflow-x: hidden;
}

/* Hero Banner Slider */
.hero-banner-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Swiper Customization */
.banner-slider .swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.banner-slider .swiper-pagination-bullet-active {
    background: #fff;
}

.banner-slider .swiper-button-next,
.banner-slider .swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

/* Features Section */
.features-section {
    position: relative;
    z-index: 10;
    margin-top: -100px;
    padding-top: 120px;
}

.feature-card {
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(76, 175, 80, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--premium-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #fff;
    margin-bottom: 1.5rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.section-divider {
    width: 80px;
    height: 4px;
    background: var(--premium-primary);
    border-radius: 2px;
    margin: 0 auto 3rem;
}

/* Dishes Section */
.dishes-section {
    padding: 5rem 0;
}

.dish-card {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.dish-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.dish-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.dish-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.dish-card:hover .dish-image img {
    transform: scale(1.1);
}

.dish-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dish-card:hover .dish-overlay {
    opacity: 1;
}

.dish-actions {
    display: flex;
    gap: 10px;
}

.dish-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dish-badge.available {
    background: #28a745;
    color: #fff;
}

.dish-badge.sold-out {
    background: #dc3545;
    color: #fff;
}

.dish-content {
    padding: 1.5rem;
}

.dish-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.dish-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.dish-price {
    font-size: 1.25rem;
    font-weight: 800;
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    padding: 5px 12px;
    border-radius: 10px;
}

.dish-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.diet-badge {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.diet-badge.veg {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.diet-badge.non-veg {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.dish-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.dish-rating i {
    color: #ffc107;
    font-size: 0.9rem;
}

.rating-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-left: 5px;
}

.dish-description {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

/* Reviews Section */
.reviews-section {
    padding: 5rem 0;
}

.review-card {
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--premium-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 700;
    font-size: 1.2rem;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.review-date {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 2px;
}

.review-rating {
    display: flex;
    gap: 2px;
}

.review-rating i {
    color: #ffc107;
    font-size: 1rem;
}

.review-content p {
    color: #495057;
    line-height: 1.6;
    margin: 0;
    font-style: italic;
}

/* Call to Action Section */
.cta-section {
    background: var(--premium-primary);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
    opacity: 0.1;
    z-index: 0;
}

.cta-section .container {
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #fff;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-section {
        margin-top: -50px;
        padding-top: 70px;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .dish-header {
        flex-direction: column;
        gap: 10px;
    }

    .cta-title {
        font-size: 2rem;
    }

    .btn-premium {
        padding: 10px 25px;
        font-size: 0.9rem;
    }
}
