<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<!-- Main Container -->
<div class="home-container">

    <!-- Hero Banner Slider Section -->
    <section class="hero-banner-section">
        <div class="banner-slider swiper">
            <div class="swiper-wrapper">
                <!-- Default Hero Slide -->
                <div class="swiper-slide">
                    <div class="hero-slide" style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(46, 125, 50, 0.9) 100%), url('https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;">
                        <div class="container">
                            <div class="row align-items-center min-vh-75">
                                <div class="col-lg-6">
                                    <div class="hero-content" data-aos="fade-right">
                                        <h1 class="hero-title text-white mb-4">
                                            Premium <span class="text-warning">Homemade</span><br>
                                            Tiffin Service
                                        </h1>
                                        <p class="hero-subtitle text-white mb-4">
                                            Experience the finest homemade meals crafted with love and delivered fresh to your doorstep. Taste the difference quality makes.
                                        </p>
                                        <div class="hero-buttons">
                                            <a href="<?= base_url('/dishes') ?>" class="btn btn-premium btn-lg me-3">
                                                <i class="fas fa-utensils me-2"></i> Explore Menu
                                            </a>
                                            <?php if (!session()->get('logged_in')): ?>
                                                <a href="<?= base_url('/auth/register') ?>" class="btn btn-outline-light btn-lg">
                                                    <i class="fas fa-user-plus me-2"></i> Join Now
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= base_url('/booking/create') ?>" class="btn btn-outline-light btn-lg">
                                                    <i class="fas fa-shopping-cart me-2"></i> Order Now
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="hero-image" data-aos="fade-left">
                                        <div class="floating-card">
                                            <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                                class="img-fluid rounded-4" alt="Delicious Food">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Banner Slides from Database -->
                <?php if (!empty($banners)): ?>
                    <?php foreach ($banners as $banner): ?>
                        <div class="swiper-slide">
                            <div class="hero-slide" style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%), url('<?= base_url('/uploads/banners/' . $banner['image']) ?>') center/cover;">
                                <div class="container">
                                    <div class="row align-items-center min-vh-75">
                                        <div class="col-12 text-center">
                                            <div class="banner-content" data-aos="fade-up">
                                                <h1 class="hero-title text-white mb-4">
                                                    <?= $banner['title'] ?>
                                                </h1>
                                                <?php if ($banner['subtitle']): ?>
                                                    <p class="hero-subtitle text-white mb-4">
                                                        <?= $banner['subtitle'] ?>
                                                    </p>
                                                <?php endif; ?>
                                                <?php if ($banner['button_text'] && $banner['button_link']): ?>
                                                    <div class="banner-buttons">
                                                        <a href="<?= base_url($banner['button_link']) ?>" class="btn btn-premium btn-lg">
                                                            <?= $banner['button_text'] ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Slider Controls -->
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <!-- Premium Features Section -->
    <section class="features-section py-5 bg-light">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="feature-content">
                            <h5 class="feature-title">Premium Ingredients</h5>
                            <p class="feature-description">Sourced from the finest local farms, ensuring freshness and quality in every bite.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <div class="feature-content">
                            <h5 class="feature-title">Lightning Delivery</h5>
                            <p class="feature-description">Hot, fresh meals delivered to your doorstep within 30 minutes guaranteed.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h5 class="feature-title">Secure Payments</h5>
                            <p class="feature-description">Multiple secure payment options with 100% money-back guarantee.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Premium Dishes Section -->
    <section class="dishes-section py-5 bg-white">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center" data-aos="fade-up">
                    <h2 class="section-title">Our Signature Dishes</h2>
                    <p class="section-subtitle">Handcrafted with love, served with passion</p>
                    <div class="section-divider"></div>
                </div>
            </div>

            <?php if (empty($featured_dishes)): ?>
                <div class="empty-state">
                    <i class="fas fa-utensils"></i>
                    <h4>No dishes available</h4>
                    <p>Check back soon for delicious new additions!</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($featured_dishes as $index => $dish): ?>
                        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?= ($index + 1) * 100 ?>">
                            <div class="dish-card">
                                <div class="dish-image">
                                    <?php if ($dish['image']): ?>
                                        <img src="<?= base_url('/uploads/dishes/' . $dish['image']) ?>" alt="<?= $dish['name'] ?>">
                                    <?php else: ?>
                                        <?php
                                        $placeholders = [
                                            'https://images.unsplash.com/photo-1567337710282-00832b415979?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                                            'https://images.unsplash.com/photo-1589302168068-964664d93dc0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                                            'https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
                                        ];
                                        $placeholderIndex = $dish['id'] % count($placeholders);
                                        $placeholder = $placeholders[$placeholderIndex];
                                        ?>
                                        <img src="<?= $placeholder ?>" alt="<?= $dish['name'] ?>">
                                    <?php endif; ?>

                                    <div class="dish-overlay">
                                        <div class="dish-actions">
                                            <?php if ($dish['available']): ?>
                                                <a href="<?= base_url('/booking/add-to-cart/' . $dish['id']) ?>" class="btn btn-premium btn-sm">
                                                    <i class="fas fa-cart-plus"></i>
                                                </a>
                                            <?php endif; ?>
                                            <a href="<?= base_url('/dishes/view/' . $dish['id']) ?>" class="btn btn-outline-light btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <?php if ($dish['available']): ?>
                                        <div class="dish-badge available">Available</div>
                                    <?php else: ?>
                                        <div class="dish-badge sold-out">Sold Out</div>
                                    <?php endif; ?>
                                </div>

                                <div class="dish-content">
                                    <div class="dish-header">
                                        <h5 class="dish-title"><?= $dish['name'] ?></h5>
                                        <span class="dish-price">₹<?= number_format($dish['price'], 2) ?></span>
                                    </div>

                                    <div class="dish-meta">
                                        <?php if (isset($dish['is_vegetarian'])): ?>
                                            <?php if ($dish['is_vegetarian']): ?>
                                                <span class="diet-badge veg">
                                                    <i class="fas fa-leaf"></i> Veg
                                                </span>
                                            <?php else: ?>
                                                <span class="diet-badge non-veg">
                                                    <i class="fas fa-drumstick-bite"></i> Non-Veg
                                                </span>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <div class="dish-rating">
                                            <?php
                                            $db = \Config\Database::connect();
                                            $builder = $db->table('reviews');
                                            $builder->select('AVG(rating) as avg_rating, COUNT(*) as review_count');
                                            $builder->join('booking_items', 'booking_items.booking_id = reviews.booking_id');
                                            $builder->where('booking_items.dish_id', $dish['id']);
                                            $result = $builder->get()->getRowArray();

                                            $avgRating = round($result['avg_rating'] ?? 0, 1);
                                            $reviewCount = $result['review_count'] ?? 0;

                                            for ($i = 1; $i <= 5; $i++):
                                                if ($i <= floor($avgRating)): ?>
                                                    <i class="fas fa-star"></i>
                                                <?php elseif ($i - 0.5 <= $avgRating): ?>
                                                    <i class="fas fa-star-half-alt"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star"></i>
                                            <?php endif;
                                            endfor; ?>
                                            <span class="rating-text">(<?= $reviewCount > 0 ? $avgRating : 'New' ?>)</span>
                                        </div>
                                    </div>

                                    <p class="dish-description"><?= substr($dish['description'], 0, 80) . (strlen($dish['description']) > 80 ? '...' : '') ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-5">
                    <a href="<?= base_url('/dishes') ?>" class="btn btn-premium btn-lg">
                        View All Menu Items <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Customer Reviews Section -->
    <section class="reviews-section py-5 bg-light">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center" data-aos="fade-up">
                    <h2 class="section-title">What Our Customers Say</h2>
                    <p class="section-subtitle">Real reviews from real customers</p>
                    <div class="section-divider"></div>
                </div>
            </div>

            <div class="row">
                <?php if (empty($reviews)): ?>
                    <!-- Default reviews if no reviews in database -->
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                        <div class="review-card">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Priya Sharma">
                                    </div>
                                    <div class="reviewer-details">
                                        <h6 class="reviewer-name">Priya Sharma</h6>
                                        <div class="review-date">
                                            <i class="far fa-calendar-alt"></i> Jan 15, 2024
                                        </div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="review-content">
                                <p>"The food is absolutely delicious and reminds me of home-cooked meals. The delivery is always on time and the staff is very friendly."</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="review-card">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Rahul Verma">
                                    </div>
                                    <div class="reviewer-details">
                                        <h6 class="reviewer-name">Rahul Verma</h6>
                                        <div class="review-date">
                                            <i class="far fa-calendar-alt"></i> Feb 20, 2024
                                        </div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                            </div>
                            <div class="review-content">
                                <p>"I've been ordering from Tiffin Delight for the past 3 months and I'm extremely satisfied with their service. The food is fresh, tasty, and reasonably priced."</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                        <div class="review-card">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Ananya Patel">
                                    </div>
                                    <div class="reviewer-details">
                                        <h6 class="reviewer-name">Ananya Patel</h6>
                                        <div class="review-date">
                                            <i class="far fa-calendar-alt"></i> Mar 5, 2024
                                        </div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="review-content">
                                <p>"As a busy professional, Tiffin Delight has been a lifesaver. The food is nutritious and delicious, and the wallet system makes payments so convenient!"</p>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Display actual reviews from database -->
                    <?php foreach ($reviews as $index => $review): ?>
                        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?= ($index + 1) * 100 ?>">
                            <div class="review-card">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <div class="reviewer-avatar">
                                            <span><?= substr($review['user_name'], 0, 1) ?></span>
                                        </div>
                                        <div class="reviewer-details">
                                            <h6 class="reviewer-name"><?= $review['user_name'] ?></h6>
                                            <div class="review-date">
                                                <i class="far fa-calendar-alt"></i> <?= date('M d, Y', strtotime($review['created_at'])) ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="review-rating">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <?php if ($i <= $review['rating']): ?>
                                                <i class="fas fa-star"></i>
                                            <?php else: ?>
                                                <i class="far fa-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <div class="review-content">
                                    <?php if (!empty($review['comment'])): ?>
                                        <p><?= nl2br($review['comment']) ?></p>
                                    <?php else: ?>
                                        <p>Great food and service!</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div class="text-center mt-5">
                <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                    <a href="<?= base_url('/user/bookings') ?>" class="btn btn-premium btn-lg">
                        <i class="fas fa-star me-2"></i> Leave Your Review
                    </a>
                <?php else: ?>
                    <a href="<?= base_url('/auth/register') ?>" class="btn btn-premium btn-lg">
                        <i class="fas fa-user-plus me-2"></i> Join Our Happy Customers
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8" data-aos="fade-right">
                    <h2 class="cta-title">Ready to Order Your Tiffin?</h2>
                    <p class="cta-subtitle">Get delicious, homemade food delivered to your doorstep today!</p>
                </div>
                <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                    <?php if (!session()->get('logged_in')): ?>
                        <a href="<?= base_url('/auth/register') ?>" class="btn btn-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i> Register Now
                        </a>
                    <?php else: ?>
                        <a href="<?= base_url('/booking/create') ?>" class="btn btn-light btn-lg">
                            <i class="fas fa-shopping-cart me-2"></i> Order Now
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

</div>
<!-- End Main Container -->

<?= $this->endSection() ?>