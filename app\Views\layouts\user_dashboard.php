<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title') ?> | Tiffin Delight</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Premium Style CSS -->
    <link rel="stylesheet" href="<?= base_url('/assets/css/premium-style.css') ?>">
</head>

<body class="bg-light">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="<?= base_url('/') ?>">
                <i class="fas fa-utensils me-2"></i>Tiffin Delight
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="bg-white text-success rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                            <span class="fw-bold"><?= strtoupper(substr(session()->get('name'), 0, 1)) ?></span>
                        </div>
                        <?= session()->get('name') ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?= base_url('/user/profile') ?>"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('/') ?>"><i class="fas fa-home me-2"></i>Home</a></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item text-danger" href="<?= base_url('/auth/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarOffcanvas">
        <div class="offcanvas-header bg-success text-white">
            <h5 class="offcanvas-title">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body p-0">
            <div class="list-group list-group-flush">
                <a href="<?= base_url('/user/dashboard') ?>" class="list-group-item list-group-item-action <?= (current_url() == base_url('/user/dashboard')) ? 'active' : '' ?>">
                    <i class="fas fa-tachometer-alt me-3"></i>Dashboard
                </a>
                <a href="<?= base_url('/user/profile') ?>" class="list-group-item list-group-item-action <?= (current_url() == base_url('/user/profile')) ? 'active' : '' ?>">
                    <i class="fas fa-user-edit me-3"></i>Profile Settings
                </a>
                <a href="<?= base_url('/user/wallet') ?>" class="list-group-item list-group-item-action <?= (current_url() == base_url('/user/wallet')) ? 'active' : '' ?>">
                    <i class="fas fa-wallet me-3"></i>My Wallet
                </a>
                <a href="<?= base_url('/user/bookings') ?>" class="list-group-item list-group-item-action <?= (current_url() == base_url('/user/bookings')) ? 'active' : '' ?>">
                    <i class="fas fa-list-alt me-3"></i>My Bookings
                </a>
                <a href="<?= base_url('/dishes') ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-utensils me-3"></i>Browse Menu
                </a>
                <a href="<?= base_url('/booking/create') ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-calendar-plus me-3"></i>Book Tiffin
                </a>
                <a href="<?= base_url('/') ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-home me-3"></i>Back to Home
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-1 fw-bold text-dark"><?= $this->renderSection('page_title') ?></h1>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-0">
                                        <li class="breadcrumb-item"><a href="<?= base_url('/user/dashboard') ?>" class="text-decoration-none">Dashboard</a></li>
                                        <?= $this->renderSection('breadcrumb') ?>
                                    </ol>
                                </nav>
                            </div>
                            <div class="d-none d-md-block">
                                <button class="btn btn-outline-success" data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas">
                                    <i class="fas fa-bars me-2"></i>Menu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Body -->
        <div class="content-body">
            <?= $this->renderSection('content') ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <?= $this->renderSection('scripts') ?>
</body>

</html>